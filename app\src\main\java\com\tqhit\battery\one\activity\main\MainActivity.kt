package com.tqhit.battery.one.activity.main

import android.app.ActivityManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.applovin.mediation.MaxAd
import com.applovin.mediation.MaxAdViewAdListener
import com.applovin.mediation.MaxError
import com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper
import com.tqhit.battery.one.BatteryApplication
import com.tqhit.battery.one.R
import com.tqhit.battery.one.databinding.ActivityMainBinding
import com.tqhit.battery.one.features.stats.charge.repository.StatsChargeRepository
import com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment
import com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository
import com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerServiceHelper
import com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment
import com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationServiceHelper
import com.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager
import com.tqhit.battery.one.features.navigation.DynamicNavigationManager
import com.tqhit.battery.one.features.navigation.AppNavigator
import com.tqhit.battery.one.features.navigation.SharedNavigationViewModel
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import com.tqhit.battery.one.fragment.main.HealthFragment
import com.tqhit.battery.one.fragment.main.SettingsFragment
import com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment
import com.tqhit.battery.one.repository.AppRepository
import com.tqhit.battery.one.service.ChargingOverlayService
import com.tqhit.battery.one.utils.AntiThiefUtils
import com.tqhit.battery.one.ads.core.ApplovinBannerAdManager
import com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager
import com.tqhit.battery.one.utils.DeviceUtils
import com.tqhit.battery.one.utils.BackgroundPermissionManager
import com.tqhit.battery.one.dialog.permission.BackgroundPermissionDialog
import com.tqhit.battery.one.activity.main.handlers.NavigationHandler
import com.tqhit.battery.one.activity.main.handlers.ServiceManager
import com.tqhit.battery.one.activity.main.handlers.FragmentLifecycleManager
import com.tqhit.battery.one.dialog.utils.NotificationDialog
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeout
import javax.inject.Inject

@AndroidEntryPoint
class MainActivity : AdLibBaseActivity<ActivityMainBinding>() {
    override val binding by lazy { ActivityMainBinding.inflate(layoutInflater) }

    // Use statsChargeRepository directly instead of BatteryViewModel to avoid crashes
    @Inject lateinit var statsChargeRepository: StatsChargeRepository
    @Inject lateinit var appRepository: AppRepository
    @Inject lateinit var applovinBannerAdManager: ApplovinBannerAdManager
    @Inject lateinit var remoteConfigHelper: FirebaseRemoteConfigHelper
    // Legacy chargeMonitorServiceHelper injection removed - replaced by UnifiedBatteryNotificationService
    @Inject lateinit var enhancedDischargeTimerServiceHelper: EnhancedDischargeTimerServiceHelper
    @Inject lateinit var dischargeSessionRepository: DischargeSessionRepository
    @Inject lateinit var unifiedBatteryNotificationServiceHelper: UnifiedBatteryNotificationServiceHelper
    @Inject lateinit var usageStatsPermissionManager: UsageStatsPermissionManager
    @Inject lateinit var dynamicNavigationManager: DynamicNavigationManager
    @Inject lateinit var appNavigator: AppNavigator
    @Inject lateinit var coreBatteryStatsProvider: CoreBatteryStatsProvider

    // Handler classes for improved maintainability
    @Inject lateinit var navigationHandler: NavigationHandler
    @Inject lateinit var serviceManager: ServiceManager
    @Inject lateinit var fragmentLifecycleManager: FragmentLifecycleManager

    // SharedNavigationViewModel for fragment state management
    private val sharedNavigationViewModel: SharedNavigationViewModel by viewModels()

    @Inject
    lateinit var applovinInterstitialAdManager: ApplovinInterstitialAdManager

    private var isInitialFragmentSet = false
    private var currentSelectedItemId: Int = R.id.chargeFragment
    private var isFragmentSetupInProgress = false
    private val handler = Handler(Looper.getMainLooper())
    private var savedInstanceState: Bundle? = null

    // Background permission dialog
    private var backgroundPermissionDialog: BackgroundPermissionDialog? = null

    // Add a receiver for handling animation-related events on Xiaomi devices
    private val animationFixReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent?.action == "android.intent.action.SCREEN_ON" ||
                intent?.action == "android.intent.action.USER_PRESENT") {
                // When screen turns on or user unlocks, refresh the UI to prevent animation issues
                if (DeviceUtils.isXiaomiDevice()) {
                    try {
                        Log.d(TAG, "Screen state changed, refreshing UI for Xiaomi device")
                        binding.root.invalidate()
                        binding.navHostFragment.invalidate()
                    } catch (e: Exception) {
                        Log.e(TAG, "Error handling screen state change", e)
                    }
                }
            }
        }
    }

    companion object {
        private const val TAG = "MainActivity"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.d(TAG, "NAVIGATION_RESTORE: onCreate: Starting main activity")

        // Store savedInstanceState for later use in navigation setup
        this.savedInstanceState = savedInstanceState

        // Handle state restoration with detailed logging
        val isStateRestoration = savedInstanceState != null
        Log.d(TAG, "NAVIGATION_RESTORE: Is state restoration: $isStateRestoration")

        // State restoration is now handled by NavigationHandler
        savedInstanceState?.let {
            val restoredItemId = it.getInt("selected_item_id", R.id.animationGridFragment)
            Log.d(TAG, "NAVIGATION_RESTORE: Restored selectedItemId: $restoredItemId")
        }

        // Initialize usage stats permission manager early in lifecycle
        try {
            usageStatsPermissionManager.initializePermissionLauncher(this)
            Log.d(TAG, "UsageStatsPermissionManager initialized successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing UsageStatsPermissionManager", e)
        }

        // Apply additional Xiaomi-specific handling if needed
        applyXiaomiSpecificHandling()

        // Check and show background permission dialog if needed
        checkAndShowBackgroundPermissionDialog()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        navigationHandler.saveNavigationState(outState, binding.bottomView)
    }

    /**
     * Sets up dynamic navigation manager for real-time charging state switching
     */
    private fun setupDynamicNavigation() {
        Log.d(TAG, "Setting up dynamic navigation using NavigationHandler")

        try {
            navigationHandler.setupDynamicNavigation(
                fragmentManager = supportFragmentManager,
                bottomNavigationView = binding.bottomView,
                fragmentContainerId = binding.navHostFragment.id,
                lifecycleOwner = this,
                sharedNavigationViewModel = sharedNavigationViewModel,
                savedInstanceState = savedInstanceState
            ) { itemId ->

                applovinInterstitialAdManager.showInterstitialAd(
                    "default_iv",
                    this,
                )
                val navigationStartTime = System.currentTimeMillis()
                Log.d(
                    TAG,
                    "NAVIGATION_PERFORMANCE: Navigation item selected: ${itemId} at $navigationStartTime"
                )

                // Let the dynamic navigation manager handle the navigation
                val handled = dynamicNavigationManager.handleUserNavigation(itemId)

                if (handled) {
                    currentSelectedItemId = itemId
                    val navigationTime = System.currentTimeMillis() - navigationStartTime
                    Log.d(
                        TAG,
                        "NAVIGATION_PERFORMANCE: Navigation handled by dynamic manager in ${navigationTime}ms"
                    )

                    // Log performance stats periodically
                    if (navigationTime > 100) { // Log if navigation takes more than 100ms
                        Log.i(
                            TAG,
                            "NAVIGATION_PERFORMANCE: Slow navigation detected (${navigationTime}ms)"
                        )
                        Log.i(TAG, dynamicNavigationManager.getPerformanceStats())
                    }
                } else {
                    Log.w(
                        TAG,
                        "NAVIGATION_RESTORE: Navigation not handled by dynamic manager, falling back to manual handling"
                    )
                    // Fallback to manual fragment switching for non-dynamic items
                    handleManualNavigation(itemId)
                    val fallbackTime = System.currentTimeMillis() - navigationStartTime
                    Log.d(
                        TAG,
                        "NAVIGATION_PERFORMANCE: Manual navigation completed in ${fallbackTime}ms"
                    )
                }

                handled
            }
        } catch (e: Exception) {
            Log.e(TAG, "NAVIGATION_RESTORE: Error setting up dynamic navigation via handler", e)
        }
    }

    private fun showInfoDialog(titleResId: Int, messageResId: Int) {
            NotificationDialog(this, getString(titleResId), getString(messageResId)).show()
    }
    /**
     * Handles manual navigation for items not managed by dynamic navigation
     */
    private fun handleManualNavigation(itemId: Int) {
        navigationHandler.handleManualNavigation(
            itemId = itemId,
            fragmentManager = supportFragmentManager,
            fragmentContainerId = binding.navHostFragment.id
        )
    }

    /**
     * FRAGMENT_LIFECYCLE_FIX: Navigate to Others fragment using centralized AppNavigator
     * This preserves fragment lifecycle and prevents ViewModel destruction during navigation.
     * Called by DischargeFragment's back navigation to maintain data consistency.
     */
    fun navigateToOthersFragment() {
        Log.d(TAG, "FRAGMENT_LIFECYCLE_FIX: navigateToOthersFragment() called")
        Log.d(TAG, "Navigation: Using AppNavigator for centralized navigation management")

        // Initialize AppNavigator if not already done
        initializeAppNavigatorIfNeeded()

        try {
            // Try AppNavigator first (modern centralized approach)
            val navigationSuccess = appNavigator.navigateToOthers()

            if (navigationSuccess) {
                Log.d(TAG, "FRAGMENT_LIFECYCLE_FIX: Successfully navigated to Others fragment using AppNavigator")
                Log.d(TAG, "Navigation: Fragment lifecycle preserved - ViewModel should remain intact")
            } else {
                Log.w(TAG, "FRAGMENT_LIFECYCLE_FIX: AppNavigator navigation failed - using legacy fallback")
                navigateToOthersFragmentLegacy()
            }

        } catch (e: Exception) {
            Log.e(TAG, "FRAGMENT_LIFECYCLE_FIX: Error during AppNavigator navigation to Others fragment", e)
            Log.e(TAG, "Navigation: Falling back to legacy navigation")
            navigateToOthersFragmentLegacy()
        }
    }

    /**
     * Initializes AppNavigator if not already initialized.
     * This ensures centralized navigation is available when needed.
     */
    private fun initializeAppNavigatorIfNeeded() {
        if (!appNavigator.isInitialized()) {
            Log.d(TAG, "APPNAVIGATOR_INIT: Initializing AppNavigator from MainActivity")
            try {
                appNavigator.initialize(
                    fragmentManager = supportFragmentManager,
                    bottomNavigationView = binding.bottomView,
                    fragmentContainerId = binding.navHostFragment.id,
                    lifecycleOwner = this
                )
                Log.d(TAG, "APPNAVIGATOR_INIT: AppNavigator initialized successfully")
            } catch (e: Exception) {
                Log.e(TAG, "APPNAVIGATOR_INIT: Error initializing AppNavigator", e)
            }
        }
    }

    /**
     * Legacy navigation to Others fragment using DynamicNavigationManager.
     * Used as fallback when AppNavigator fails.
     */
    private fun navigateToOthersFragmentLegacy() {
        Log.d(TAG, "FRAGMENT_LIFECYCLE_FIX: Using legacy DynamicNavigationManager navigation")

        try {
            // Get the DynamicNavigationManager from NavigationHandler
            val dynamicNavigationManager = navigationHandler.getDynamicNavigationManager()

            if (dynamicNavigationManager != null) {
                // Use DynamicNavigationManager's handleUserNavigation to preserve fragment state
                val navigationSuccess = dynamicNavigationManager.handleUserNavigation(R.id.othersFragment)

                if (navigationSuccess) {
                    Log.d(TAG, "FRAGMENT_LIFECYCLE_FIX: Successfully navigated to Others fragment using DynamicNavigationManager")
                    Log.d(TAG, "Navigation: Fragment lifecycle preserved - ViewModel should remain intact")

                    // Update bottom navigation to reflect the change
                    binding.bottomView.selectedItemId = R.id.othersFragment

                } else {
                    Log.w(TAG, "FRAGMENT_LIFECYCLE_FIX: DynamicNavigationManager navigation failed - using manual fallback")
                    Log.w(TAG, "Navigation: Manual fallback may not preserve fragment lifecycle")

                    // Fallback: Use manual navigation (this may still use replace pattern)
                    handleManualNavigation(R.id.othersFragment)
                    binding.bottomView.selectedItemId = R.id.othersFragment
                }
            } else {
                Log.w(TAG, "FRAGMENT_LIFECYCLE_FIX: DynamicNavigationManager not available - using manual navigation")
                handleManualNavigation(R.id.othersFragment)
                binding.bottomView.selectedItemId = R.id.othersFragment
            }

        } catch (e: Exception) {
            Log.e(TAG, "FRAGMENT_LIFECYCLE_FIX: Error during legacy navigation to Others fragment", e)
            Log.e(TAG, "Navigation: Failed to preserve fragment lifecycle during navigation")

            // Emergency fallback
            try {
                handleManualNavigation(R.id.othersFragment)
                binding.bottomView.selectedItemId = R.id.othersFragment
                Log.d(TAG, "FRAGMENT_LIFECYCLE_FIX: Emergency fallback navigation completed")
            } catch (fallbackException: Exception) {
                Log.e(TAG, "FRAGMENT_LIFECYCLE_FIX: All navigation attempts failed", fallbackException)
            }
        }
    }

    /**
     * Optimized fragment setup to prevent multiple recreations and improve startup performance
     * Now works with dynamic navigation manager via FragmentLifecycleManager
     */
    private fun setupInitialFragmentOptimized(startTime: Long) {
        Log.d(TAG, "Setting up initial fragment using FragmentLifecycleManager")

        fragmentLifecycleManager.setupInitialFragmentOptimized(
            fragmentManager = supportFragmentManager,
            fragmentContainerId = binding.navHostFragment.id,
            lifecycleOwner = this,
            navigationHandler = navigationHandler,
            startTime = startTime
        )
    }

    /**
     * Legacy fragment setup as fallback when modern navigation fails.
     * NOTE: This method is kept for backward compatibility but should rarely be used
     * now that we have centralized AppNavigator and SharedNavigationViewModel.
     */
    private fun setupLegacyInitialFragment(startTime: Long) {
        lifecycleScope.launch {
            try {
                val batteryStatusStartTime = System.currentTimeMillis()

                // Use background thread for battery status retrieval with timeout to avoid blocking UI
                val initialBatteryStatus = withContext(Dispatchers.IO) {
                    try {
                        // Get status from CoreBatteryStatsProvider with timeout
                        withTimeout(500) {
                            coreBatteryStatsProvider.getCurrentStatus()
                                ?: com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus.createDefault()
                        }
                    } catch (e: Exception) {
                        Log.w(TAG, "Timeout getting battery status from core provider", e)
                        // Return default status if timeout
                        com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus.createDefault()
                    }
                }

                Log.d(TAG, "STARTUP_TIMING: Battery status retrieval took ${System.currentTimeMillis() - batteryStatusStartTime}ms")

                val isCharging = initialBatteryStatus.isCharging

                // Switch back to main thread for UI operations
                withContext(Dispatchers.Main) {
                    if (!navigationHandler.isInitialFragmentSet()) {
                        Log.d(TAG, "Legacy initial fragment setup - charging state: $isCharging")
                        val fragmentStartTime = System.currentTimeMillis()

                        // For 2-button navigation, always start with Animation fragment
                        val initialFragment = AnimationGridFragment()
                        val initialItemId = R.id.animationGridFragment

                        // Use commitNow() for immediate execution to prevent timing issues
                        supportFragmentManager
                            .beginTransaction()
                            .replace(binding.navHostFragment.id, initialFragment)
                            .commitNow()

                        binding.bottomView.selectedItemId = initialItemId
                        navigationHandler.setInitialFragmentSet(true)

                        Log.d(TAG, "STARTUP_TIMING: Legacy fragment setup took ${System.currentTimeMillis() - fragmentStartTime}ms")
                    }

                    Log.d(TAG, "STARTUP_TIMING: MainActivity.setupUI() completed in ${System.currentTimeMillis() - startTime}ms")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error in legacy fragment setup", e)
            }
        }
    }

    private fun isServiceRunning(serviceClass: Class<*>): Boolean {
        val activityManager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        return activityManager.getRunningServices(Integer.MAX_VALUE)
                .any { it.service.className == serviceClass.name }
    }

    override fun setupUI() {
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "STARTUP_TIMING: MainActivity.setupUI() started at $startTime")

        super.setupUI()
        Log.d(TAG, "setupUI: Setting up main UI")

        // Initialize centralized AppNavigator early
        initializeAppNavigatorIfNeeded()

        // Initialize dynamic navigation manager
        setupDynamicNavigation()

        // Set initial fragment based on charging state only once with optimization
        setupInitialFragmentOptimized(startTime)

        // Initialize MAX SDK after UI is ready to avoid blocking startup
        initializeMaxSdkDeferred()

        // --- Banner Ad Integration ---
        initBannerAd()
    }

    private fun restoreFragmentState() {
        navigationHandler.restoreFragmentState(
            fragmentManager = supportFragmentManager,
            bottomNavigationView = binding.bottomView,
            fragmentContainerId = binding.navHostFragment.id
        )
    }

    override fun onResume() {
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "STARTUP_TIMING: MainActivity.onResume() started at $startTime")
        Log.d(TAG, "NAVIGATION_RESTORE: onResume called - currentSelectedItemId: ${navigationHandler.getCurrentSelectedItemId()}")

        super.onResume()
        Log.d(TAG, "NAVIGATION_RESTORE: onResume: Restoring fragment state and starting services")

        // Apply any necessary device-specific resume handling
        if (DeviceUtils.isXiaomiDevice()) {
            try {
                // Handle any Xiaomi-specific issues when resuming
                Log.d(TAG, "Applying Xiaomi-specific resume handling")

                // Register the animation fix receiver
                val intentFilter = IntentFilter().apply {
                    addAction("android.intent.action.SCREEN_ON")
                    addAction("android.intent.action.USER_PRESENT")
                }
                registerReceiver(animationFixReceiver, intentFilter)

                // Re-apply layout settings for MIUI compatibility
                binding.root.requestLayout()
                binding.navHostFragment.requestLayout()
            } catch (e: Exception) {
                Log.e(TAG, "Error in Xiaomi resume handling", e)
            }
        }

        // Restore fragment state when activity resumes
        restoreFragmentState()

        // Check and show background permission dialog if needed
        checkAndShowBackgroundPermissionDialog()

        // Start critical services using ServiceManager
        serviceManager.startCriticalServices(this, this)

        // Defer non-critical service startup to background thread
        serviceManager.startNonCriticalServicesAsync(this, this)

        // Log navigation performance stats
        navigationHandler.logNavigationPerformanceStats()

        Log.d(TAG, "STARTUP_TIMING: MainActivity.onResume() completed in ${System.currentTimeMillis() - startTime}ms")
    }



    override fun onPause() {
        // Unregister the animation fix receiver if it was registered
        if (DeviceUtils.isXiaomiDevice()) {
            try {
                unregisterReceiver(animationFixReceiver)
            } catch (e: Exception) {
                Log.e(TAG, "Error unregistering animation fix receiver", e)
            }
        }

        super.onPause()
    }



    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "onDestroy called")
        if (binding.bannerContainer.isEnabled)
            applovinBannerAdManager.destroy(binding.bannerContainer)

        // Clean up background permission dialog
        backgroundPermissionDialog?.dismiss()
        backgroundPermissionDialog = null

        // Unregister Xiaomi-specific receiver if registered
        if (DeviceUtils.isXiaomiDevice()) {
            try {
                unregisterReceiver(animationFixReceiver)
            } catch (e: Exception) {
                Log.d(TAG, "Animation fix receiver was not registered or already unregistered")
            }
        }
    }

    private fun initBannerAd() {
        if (remoteConfigHelper.getBoolean("bn_enable")) {
            binding.bannerContainer.visibility = View.VISIBLE

            // Defer banner ad loading to avoid blocking UI startup
            // Wait for MAX SDK to be initialized before attempting to load ads
            handler.postDelayed({
                try {
                    applovinBannerAdManager.loadAd(
                        "default_bn",
                        binding.bannerContainer,
                        object : MaxAdViewAdListener {
                            override fun onAdLoaded(p0: MaxAd) {
                                Log.d(TAG, "Banner ad loaded successfully")
                            }

                            override fun onAdDisplayed(p0: MaxAd) {
                            }

                            override fun onAdHidden(p0: MaxAd) {
                            }

                            override fun onAdClicked(p0: MaxAd) {
                            }

                            override fun onAdLoadFailed(p0: String, p1: MaxError) {
                                Log.w(TAG, "Banner ad load failed: ${p1.message}, retrying in 5 seconds")
                                handler.postDelayed({ initBannerAd() }, 5000)
                            }

                            override fun onAdDisplayFailed(p0: MaxAd, p1: MaxError) {
                                Log.w(TAG, "Banner ad display failed: ${p1.message}")
                            }

                            override fun onAdExpanded(p0: MaxAd) {
                            }

                            override fun onAdCollapsed(p0: MaxAd) {
                            }
                        }
                    )
                } catch (e: Exception) {
                    Log.e(TAG, "Error loading banner ad, MAX SDK may not be ready yet", e)
                    // Retry after a longer delay if MAX SDK is not ready
                    handler.postDelayed({ initBannerAd() }, 10000)
                }
            }, 2000) // 2 second delay to allow MAX SDK initialization
        } else {
            binding.bannerContainer.visibility = View.GONE
        }
    }

    /**
     * Initialize MAX SDK after UI is ready to avoid blocking app startup
     */
    private fun initializeMaxSdkDeferred() {
        Log.d(TAG, "STARTUP_TIMING: Triggering deferred MAX SDK initialization")

        // Get the application instance and call the deferred initialization
        val app = application as? BatteryApplication
        app?.initializeMaxSdkWhenReady()
    }

    /**
     * Apply specific handling for Xiaomi devices to prevent crashes
     */
    private fun applyXiaomiSpecificHandling() {
        if (DeviceUtils.isXiaomiDevice()) {
            try {
                Log.d(TAG, "Applying Xiaomi-specific handling for MainActivity")

                // Set window flags to prevent animation conflicts
                window?.setFlags(
                    android.view.WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
                    android.view.WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
                )

                // Use specific drawing cache settings for better MIUI compatibility
                binding.root.setLayerType(android.view.View.LAYER_TYPE_HARDWARE, null)
                binding.navHostFragment.setLayerType(android.view.View.LAYER_TYPE_HARDWARE, null)
            } catch (e: Exception) {
                Log.e(TAG, "Error applying Xiaomi handling", e)
            }
        }
    }

    /**
     * Checks if background permission dialog should be shown and displays it if needed.
     * Uses proper Android lifecycle management and rate limiting to avoid showing during inappropriate states.
     */
    private fun checkAndShowBackgroundPermissionDialog() {
        Log.d(TAG, "BackgroundPermission: Checking if background permission dialog should be shown")

        // Don't show dialog if activity is not in appropriate state
        if (isFinishing || isDestroyed) {
            Log.d(TAG, "BackgroundPermission: Activity finishing or destroyed, skipping dialog")
            return
        }

        // Don't show dialog if one is already showing
        if (backgroundPermissionDialog?.isShowing == true) {
            Log.d(TAG, "BackgroundPermission: Dialog already showing, skipping")
            return
        }

        // Check if permission dialog should be shown (includes rate limiting)
        if (BackgroundPermissionManager.shouldShowBackgroundPermissionDialog(this)) {
            Log.d(TAG, "BackgroundPermission: Showing background permission dialog")
            showBackgroundPermissionDialog()
        } else {
            // Log specific reason why dialog is not shown
            if (BackgroundPermissionManager.isIgnoringBatteryOptimizations(this)) {
                Log.d(TAG, "BackgroundPermission: Permission already granted, no dialog needed")
            } else if (BackgroundPermissionManager.isInCooldownPeriod(this)) {
                val remainingTime = BackgroundPermissionManager.getRemainingCooldownTime(this)
                val remainingMinutes = remainingTime / (60 * 1000)
                Log.d(TAG, "BackgroundPermission: Dialog in cooldown period, ${remainingMinutes} minutes remaining")
            } else {
                Log.d(TAG, "BackgroundPermission: Dialog not shown for unknown reason")
            }
        }
    }

    /**
     * Shows the background permission dialog with proper callbacks
     */
    private fun showBackgroundPermissionDialog() {
        try {
            backgroundPermissionDialog = BackgroundPermissionDialog(
                context = this,
                onPermissionGranted = {
                    Log.d(TAG, "BackgroundPermission: Permission granted via dialog")
                    // Permission granted - no additional action needed
                },
                onPermissionDenied = {
                    Log.d(TAG, "BackgroundPermission: Permission denied via dialog")
                    // Permission denied - user chose not to grant permission
                },
                onDialogClosed = {
                    Log.d(TAG, "BackgroundPermission: Dialog closed")
                    backgroundPermissionDialog = null
                }
            )

            backgroundPermissionDialog?.show()
        } catch (e: Exception) {
            Log.e(TAG, "BackgroundPermission: Error showing background permission dialog", e)
        }
    }
}
